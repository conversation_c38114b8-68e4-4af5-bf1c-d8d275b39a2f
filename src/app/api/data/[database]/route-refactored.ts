import { type NextRequest, NextResponse } from 'next/server';
import { getDynamicModel, isPrismaModel, validateDatabaseCode, getDatabaseAccessLevel } from '@/lib/dynamicTableMapping';
import { checkPermissions } from '@/lib/server/permissions';
import { getDatabaseConfig } from '@/lib/configCache';
import type { DatabaseConfig } from '@/lib/configCache';
import { buildMedicalDeviceWhere } from '@/lib/server/buildMedicalDeviceWhere';
import { validatePaginationParams, buildPaginationResponse } from '@/lib/globalPagination';

export const dynamic = 'force-dynamic';

// --- API处理逻辑 ---

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ database: string }> }
) {
  try {
    const { database } = await params;

    // 使用新的统一验证函数
    const validationError = validateDatabaseCode(database);
    if (validationError) {
      return NextResponse.json(
        { success: false, error: validationError.error },
        { status: validationError.status }
      );
    }

    // 权限检查 - 使用动态权限获取
    const requiredLevel = getDatabaseAccessLevel(database) as "free" | "premium" | "enterprise";
    const hasAccess = await checkPermissions(requiredLevel);
    if (!hasAccess) {
      return NextResponse.json(
        { success: false, error: 'Permission denied' },
        { status: 403 }
      );
    }

    // 获取配置（带回退）
    const config: DatabaseConfig = await getDatabaseConfig(database);
    const visibleFields = config.fields.filter(f => f.isVisible).map(f => f.fieldName);
    const sortableFields = config.fields.filter(f => f.isSortable).map(f => f.fieldName);

    // --- 动态构建查询条件 ---
    const { searchParams } = new URL(request.url);

    // 使用全局翻页配置（性能优化）
    const requestedPage = Number.parseInt(searchParams.get('page') || '1');
    const requestedLimit = Number.parseInt(searchParams.get('limit') || '0');

    const { page, limit } = validatePaginationParams(requestedPage, requestedLimit);
    const sortBy = searchParams.get('sortBy') || (sortableFields[0] || 'approvalDate');
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // 使用重构后的查询构建函数 - 不再需要database字段过滤
    const where = buildMedicalDeviceWhere(searchParams, config);

    const orderBy = {
      [sortBy]: sortOrder,
    };
    
    // --- 使用动态模型获取 ---
    const model = getDynamicModel(database);
    if (!isPrismaModel(model)) {
      return NextResponse.json(
        { success: false, error: 'Model not found or invalid' },
        { status: 500 }
      );
    }

    // 只返回配置表中isVisible的字段
    const select: Record<string, boolean> = {};
    visibleFields.forEach(f => { select[f] = true; });
    // 主键id始终返回，但不再返回database字段
    select['id'] = true;

    const data = await model.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      select,
    }) as unknown[];
    
    const totalCount = await model.count({ where }) as number;
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data,
      pagination: buildPaginationResponse(page, limit, totalCount),
      filters: searchParams,
      config, // Return current configuration for frontend debugging
      databaseInfo: {
        code: database,
        requiredLevel,
        // Can add more database information
      }
    });
  } catch (__error) {
    console.error('API Error:', error);
    // In production environment, can log more detailed error information
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 