import { getDynamicModel, isPrismaModel } from './dynamicTableMapping';
import { TableConfig, ImportMode, isHashEnabled, getHashAlgorithm, UniqueKeyContext } from './uniqueKeyConfig';
import { generateBusinessKeyInfo, isValidHash } from './utils';

// 同步结果统计
export interface SyncResult {
  totalRecords: number;
  inserted: number;
  updated: number;
  skipped: number;
  errors: number;
  errorDetails: Array<{
    row: number;
    businessKey: string;
    error: string;
  }>;
  processingTime: number;
}

// 数据变更日志
export interface ChangeLog {
  businessKey: string;
  businessKeyHash?: string;
  operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'DEACTIVATE';
  oldData?: Record<string, any>;
  newData?: Record<string, any>;
  changeReason?: string;
  importedBy?: string;
  importedFrom?: string;
}

// 重构版本的智能同步引擎
export class SmartSyncEngineRefactored {
  private tableConfig!: TableConfig; // 使用!断言，因为会在setTableConfig中设置
  private context: UniqueKeyContext;
  private changeLogs: ChangeLog[] = [];
  private importedBy?: string;
  private importedFrom?: string;

  constructor(
    context: UniqueKeyContext,
    importedBy?: string, 
    importedFrom?: string
  ) {
    this.context = context;
    this.importedBy = importedBy;
    this.importedFrom = importedFrom;
  }

  // 设置表配置
  setTableConfig(config: TableConfig) {
    this.tableConfig = config;
  }

  // 字段映射处理
  private mapFields(row: Record<string, any>): Record<string, any> {
    if (!this.tableConfig.fieldMapping) {
      return row;
    }

    const mappedRow: Record<string, any> = {};
    for (const [key, value] of Object.entries(row)) {
      const mappedKey = this.tableConfig.fieldMapping[key] || key;
      mappedRow[mappedKey] = value;
    }
    return mappedRow;
  }

  // 数据验证
  private validateRow(row: Record<string, any>, rowIndex: number): string | null {
    if (!this.tableConfig.validationRules) {
      return null;
    }

    const { requiredFields, uniqueFields } = this.tableConfig.validationRules;

    // 检查必填字段
    if (requiredFields) {
      for (const field of requiredFields) {
        if (!row[field] || (typeof row[field] === 'string' && !row[field].trim())) {
          return `必填字段缺失: ${field}`;
        }
      }
    }

    return null;
  }

  // 重构版本：生成业务唯一键信息，使用上下文而非数据字段
  private generateBusinessKeyInfo(row: Record<string, any>): { businessKey: string; businessKeyHash?: string } {
    const businessKey = this.tableConfig.uniqueKeyRule(row, this.context);
    
    // 检查是否启用Hash模式
    if (isHashEnabled(this.context.tableName)) {
      const algorithm = getHashAlgorithm(this.context.tableName);
      if (algorithm !== 'none') {
        const keyInfo = generateBusinessKeyInfo(row, (r) => this.tableConfig.uniqueKeyRule(r, this.context), algorithm);
        return { businessKey, businessKeyHash: keyInfo.businessKeyHash };
      }
    }
    
    return { businessKey };
  }

  // 智能同步主函数
  async syncData(
    data: Record<string, any>[],
    mode?: ImportMode
  ): Promise<SyncResult> {
    const startTime = Date.now();
    const result: SyncResult = {
      totalRecords: data.length,
      inserted: 0,
      updated: 0,
      skipped: 0,
      errors: 0,
      errorDetails: [],
      processingTime: 0,
    };

    const importMode = mode || this.tableConfig.importMode;

    try {
      // 验证动态模型是否可用
      const model = getDynamicModel(this.context.databaseCode);
      if (!isPrismaModel(model)) {
        throw new Error(`数据库代码 ${this.context.databaseCode} 对应的模型不可用`);
      }

      // 根据导入模式选择不同的处理策略
      switch (importMode) {
        case 'insert':
          await this.handleInsertMode(data, result, model);
          break;
        case 'upsert':
          await this.handleUpsertMode(data, result, model);
          break;
        case 'replace':
          await this.handleReplaceMode(data, result, model);
          break;
        default:
          throw new Error(`不支持的导入模式: ${importMode}`);
      }

      // 记录变更日志
      await this.saveChangeLogs();

    } catch (__error) {
      console.error('同步过程中发生错误:', error);
      result.errors++;
      result.errorDetails.push({
        row: -1,
        businessKey: 'SYSTEM_ERROR',
        error: error instanceof Error ? error.message : String(error),
      });
    }

    result.processingTime = Date.now() - startTime;
    return result;
  }

  // 插入模式处理 - 重构版本
  private async handleInsertMode(
    data: Record<string, any>[],
    result: SyncResult,
    model: Record<string, unknown>
  ) {
    for (let i = 0; i < data.length; i++) {
      try {
        const row = this.mapFields(data[i]);
        const validationError = this.validateRow(row, i);
        
        if (validationError) {
          result.errors++;
          result.errorDetails.push({
            row: i + 1,
            businessKey: 'VALIDATION_ERROR',
            error: validationError,
          });
          continue;
        }

        // 生成业务唯一键信息
        const keyInfo = this.generateBusinessKeyInfo(row);
        row.businessKey = keyInfo.businessKey;
        if (keyInfo.businessKeyHash) {
          row.businessKeyHash = keyInfo.businessKeyHash;
        }

        // 检查是否已存在
        const existing = await this.findExistingRecord(keyInfo, model);
        if (existing) {
          result.skipped++;
          continue;
        }

        // 插入新记录
        await this.insertRecord(row, model);
        result.inserted++;

        // 记录变更日志
        this.changeLogs.push({
          businessKey: keyInfo.businessKey,
          businessKeyHash: keyInfo.businessKeyHash,
          operation: 'CREATE',
          newData: row,
          importedBy: this.importedBy,
          importedFrom: this.importedFrom,
        });

      } catch (__error) {
        result.errors++;
        result.errorDetails.push({
          row: i + 1,
          businessKey: 'INSERT_ERROR',
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  // Upsert模式处理 - 重构版本
  private async handleUpsertMode(
    data: Record<string, any>[],
    result: SyncResult,
    model: Record<string, unknown>
  ) {
    for (let i = 0; i < data.length; i++) {
      try {
        const row = this.mapFields(data[i]);
        const validationError = this.validateRow(row, i);
        
        if (validationError) {
          result.errors++;
          result.errorDetails.push({
            row: i + 1,
            businessKey: 'VALIDATION_ERROR',
            error: validationError,
          });
          continue;
        }

        // 生成业务唯一键信息
        const keyInfo = this.generateBusinessKeyInfo(row);
        row.businessKey = keyInfo.businessKey;
        if (keyInfo.businessKeyHash) {
          row.businessKeyHash = keyInfo.businessKeyHash;
        }

        // 查找现有记录
        const existing = await this.findExistingRecord(keyInfo, model);

        if (existing) {
          // 更新现有记录
          await this.updateRecord(keyInfo, row, model);
          result.updated++;

          // 记录变更日志
          this.changeLogs.push({
            businessKey: keyInfo.businessKey,
            businessKeyHash: keyInfo.businessKeyHash,
            operation: 'UPDATE',
            oldData: existing,
            newData: row,
            importedBy: this.importedBy,
            importedFrom: this.importedFrom,
          });
        } else {
          // 插入新记录
          await this.insertRecord(row, model);
          result.inserted++;

          // 记录变更日志
          this.changeLogs.push({
            businessKey: keyInfo.businessKey,
            businessKeyHash: keyInfo.businessKeyHash,
            operation: 'CREATE',
            newData: row,
            importedBy: this.importedBy,
            importedFrom: this.importedFrom,
          });
        }

      } catch (__error) {
        result.errors++;
        result.errorDetails.push({
          row: i + 1,
          businessKey: 'UPSERT_ERROR',
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  // 替换模式处理 - 重构版本
  private async handleReplaceMode(
    data: Record<string, any>[],
    result: SyncResult,
    model: Record<string, unknown>
  ) {
    // 先软删除所有现有记录
    await this.deactivateAllRecords(model);
    
    // 然后插入新数据
    for (let i = 0; i < data.length; i++) {
      try {
        const row = this.mapFields(data[i]);
        const validationError = this.validateRow(row, i);
        
        if (validationError) {
          result.errors++;
          result.errorDetails.push({
            row: i + 1,
            businessKey: 'VALIDATION_ERROR',
            error: validationError,
          });
          continue;
        }

        // 生成业务唯一键信息
        const keyInfo = this.generateBusinessKeyInfo(row);
        row.businessKey = keyInfo.businessKey;
        if (keyInfo.businessKeyHash) {
          row.businessKeyHash = keyInfo.businessKeyHash;
        }

        // 插入新记录
        await this.insertRecord(row, model);
        result.inserted++;

        // 记录变更日志
        this.changeLogs.push({
          businessKey: keyInfo.businessKey,
          businessKeyHash: keyInfo.businessKeyHash,
          operation: 'CREATE',
          newData: row,
          importedBy: this.importedBy,
          importedFrom: this.importedFrom,
        });

      } catch (__error) {
        result.errors++;
        result.errorDetails.push({
          row: i + 1,
          businessKey: 'REPLACE_ERROR',
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }

  // 查找现有记录 - 重构版本
  private async findExistingRecord(
    keyInfo: { businessKey: string; businessKeyHash?: string },
    model: Record<string, unknown>
  ): Promise<Record<string, any> | null> {
    try {
      // 优先使用Hash查找（如果启用Hash模式）
      if (keyInfo.businessKeyHash && isHashEnabled(this.context.tableName)) {
        const record = await model.findUnique({
          where: { businessKeyHash: keyInfo.businessKeyHash },
        });
        if (record) return record;
      }

      // 回退到原始businessKey查找
      const record = await model.findUnique({
        where: { businessKey: keyInfo.businessKey },
      });
      return record;
    } catch (__error) {
      console.error('查找现有记录时出错:', error);
      return null;
    }
  }

  // 插入记录 - 重构版本
  private async insertRecord(row: Record<string, any>, model: Record<string, unknown>): Promise<void> {
    const processedData = this.processRowData(row);
    await model.create({
      data: processedData,
    });
  }

  // 更新记录 - 重构版本
  private async updateRecord(
    keyInfo: { businessKey: string; businessKeyHash?: string },
    row: Record<string, any>,
    model: Record<string, unknown>
  ): Promise<void> {
    const processedData = this.processRowData(row);
    
    try {
      // 优先使用Hash更新（如果启用Hash模式）
      if (keyInfo.businessKeyHash && isHashEnabled(this.context.tableName)) {
        await model.update({
          where: { businessKeyHash: keyInfo.businessKeyHash },
          data: processedData,
        });
      } else {
        await model.update({
          where: { businessKey: keyInfo.businessKey },
          data: processedData,
        });
      }
    } catch (__error) {
      // 如果更新失败（记录不存在），则尝试创建
      if (error instanceof Error && error.message.includes('No record was found')) {
        console.error(`记录不存在，尝试创建新记录: ${keyInfo.businessKey}`);
        await this.insertRecord(row, model);
      } else {
        throw error;
      }
    }
  }

  // 软删除所有记录 - 重构版本
  private async deactivateAllRecords(model: Record<string, unknown>): Promise<void> {
    await model.updateMany({
      where: { isActive: true },
      data: { isActive: false },
    });
  }

  // 处理行数据（转换日期、布尔值等）
  private processRowData(row: Record<string, any>): Record<string, any> {
    const processed = { ...row };

    // 转换日期字段（支持不同表的字段名）
    const dateFields = ['approvalDate', 'validUntil', 'establishDate'];
    for (const field of dateFields) {
      if (processed[field]) {
        processed[field] = new Date(processed[field]);
      }
    }

    // 转换布尔值字段（支持不同表的字段名）
    const booleanFields = [
      'isInnovative', 'isClinicalNeed', 'isChildrenSpecific', 'isRareDisease',
      'isListed' // Company表的字段
    ];
    for (const field of booleanFields) {
      if (processed[field] !== undefined) {
        processed[field] = processed[field] === 'true' || processed[field] === '1' || processed[field] === '是';
      }
    }

    return processed;
  }

  // 保存变更日志 - 重构版本（需要使用db实例）
  private async saveChangeLogs(): Promise<void> {
    if (this.changeLogs.length === 0) {
      return;
    }

    try {
      // 注意：这里仍然需要直接使用db实例，因为变更日志是系统表
      const { db } = await import('./prisma');
      await db.dataChangeLog.createMany({
        data: this.changeLogs.map(log => ({
          businessKey: log.businessKey,
          businessKeyHash: log.businessKeyHash,
          operation: log.operation,
          oldData: log.oldData,
          newData: log.newData,
          changeReason: log.changeReason,
          importedBy: log.importedBy,
          importedFrom: log.importedFrom,
        })),
      });
    } catch (__error) {
      console.error('Error saving change logs:', error);
    }
  }
} 